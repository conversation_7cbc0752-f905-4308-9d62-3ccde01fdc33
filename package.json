{"name": "react-portfolio-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@heroicons/react": "^2.0.18", "@mui/icons-material": "^5.14.9", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.15.1", "@mui/styled-engine-sc": "^5.14.9", "@mui/system": "^5.14.13", "@nextui-org/react": "^2.2.9", "@tsparticles/react": "^3.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "framer-motion": "^12.7.4", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "mui": "^0.0.1", "nodemailer": "^6.9.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-awesome-reveal": "^4.2.6", "react-dom": "^18.2.0", "react-intersection-observer": "^9.5.3", "react-modal": "^3.16.1", "react-scroll": "^1.8.9", "react-tooltip": "^5.22.0", "styled-components": "^5.3.11", "tsparticles": "^3.8.1"}, "devDependencies": {"@types/react": "^18.0.37", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.51.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "vite": "^4.3.9"}}