@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800&display=swap");
body {
  font-family: "Poppins", sans-serif;
  background: #121213;
}

.btn1 {
  background: #0c2f60;
  border-radius: 8px;
  padding: 10px 30px;
  color: white;
}

.btn1:hover {
  background: white;
  color: rgb(22, 22, 126);
}

.outline {
  outline-color: rgb(15, 15, 78);
}

.outline:hover {
  outline-color: white;
}

.skills:first-child {
  background: #290ccb;
}

.service-card {
  width: auto;
  height: 338px;
  background: transparent;
  border: 3px solid #2b2a2f;
  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.2));
  border-radius: 16px;
  position: relative;
  transition: box-shadow 1s;
}

.service-card:hover {
  box-shadow: 0 0 10px rgba(41, 12, 203, var(--active)), 
              0 0 20px rgba(41, 12, 203, var(--active)), 
              0 0 30px rgba(41, 12, 203, var(--active)), 
              0 0 40px rgba(41, 12, 203, var(--active));
}

.service-card:nth-child(even){
  border: 3px solid white;
}
/* 
.service-card:hover{
  border-color: deepskyblue;
} */

@media (min-width: 650px) {
  .custom-home {
    height: 120vh;
  }
}
@media (min-width: 768px ) {
  .custom-home {
    height: 60vh;
  }
}
@media (min-width: 1000px) {
  .custom-home {
    height: 60vh;
  }
}
@media (min-width: 1100px) {
  .custom-home {
    height: 60vh;
  }
}

.skills {
  /* ... existing styles ... */
  color: #fff; /* Set the initial text color */

  /* Add these transition properties to smoothly transition the color */
  transition-property: transform, background-color, color;
  transition-duration: 0.3s; /* Adjust the duration as needed */
  transition-timing-function: ease-in-out;
  transform-origin: center bottom;
  transform: translateY(0);
}

.skills.hovered {
  background: rgb(22, 22, 126);
  transform: translateY(-10px); /* Adjust the translation as needed */
  color: #47b9ee; /* Change to your primary color */ /* Change to your primary color */
}

.skills.hovered p {
  color: #47b9ee;
  
}

.lighting-effect {
  transition: all 0.3s ease-out;
  position: relative;
  overflow: hidden;
}
.lighting-effect:hover {
  cursor: alias;
}
.lighting-effect:hover::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(66, 132, 179, 0.15);
  pointer-events: none;
  z-index: 1;
  transform: rotate(45deg);
  animation: lightingMove 1s;
  animation-fill-mode: forwards;
}

@keyframes lightingMove {
  0% {
      transform: translateX(-250%) rotate(45deg);
  }
  100% {
      transform: translateX(0%) rotate(45deg);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* extra glow on text hover */
.text-glow:hover {
  text-shadow: 0 0 5px #ffffff, 0 0 10px #ffffff, 0 0 15px #5fb8de, 0 0 20px #444ce0;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5rem;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5rem;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.project-card {
  width: 100%; /* Adjust the width as needed */
  margin: auto;
  display: flex;
  background-color: #0c2f60;
  border: 1px solid transparent;
  transition: border 0.3s, box-shadow 0.3s;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 10px;
}
.project-card:hover {
  border: 3px solid #007bff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
}

.icon {
  display: flex;
  justify-content: space-around;
  padding: 5%;
}
.icon:hover {
  border: 2px solid #0c2f60;
  animation: shine 0.5s linear infinite; /* shining animation */
}

@keyframes shine {
  0% {
    background-color: #fff; /* Start with white background */
  }
  50% {
    background-color: #f7f7f7; /* Midway background color */
  }
  100% {
    background-color: #fff; /* End with white background */
  }
}

/* tooltip for the mouse */
.tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background-color: white;
  padding: 20px;
  border-radius: 10px;
}

.ReactModal__Overlay {
  opacity: 0;
  transform: scale(0.95);
  transition: all 500ms ease-in-out;
}
@keyframes bounce {
  0% { transform: scale(0.95); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.ReactModal__Overlay--after-open {
  opacity: 1;
  transform: scale(1);
  animation: bounce 0.5s;
}
.ReactModal__Overlay--before-close {
  opacity: 0;
  transform: scale(0.95);
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 35vw;
  height: 45vh;
  overflow: auto;
  background: white;
  padding: 20px;
  box-shadow: 30px 22px 40rgba(153, 13, 217, 0.1);
  border-radius: 10px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 2em;
}

.modal-body {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
}

.prev-button {
  align-self: flex-start;
}

.next-button {
  align-self: flex-end;
}

.animated-text {
  animation: fadeIn 1s ease-in-out;
}

.test {
  color: rgba(203, 214, 211, 0.715)
}

/* Tech Galaxy - Modern Tech Stack Display */
.tech-galaxy {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
  width: 100%;
  position: relative;
}

.tech-group {
  position: relative;
  height: 100px;
  display: flex;
  flex-direction: column;
}

.tech-category {
  font-size: 1rem;
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.5rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  display: inline-block;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.2);
}

.tech-icons-container {
  position: relative;
  height: 80px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.tech-icon-wrapper {
  position: absolute;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  transition: all 0.3s ease;
  animation: floatAround linear infinite;
}

.tech-icon-wrapper:hover {
  transform: scale(1.2) !important;
  z-index: 20;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(71, 185, 238, 0.8);
}

.tech-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s ease;
}

.tech-icon-wrapper:hover .tech-icon-img {
  transform: rotate(-10deg) scale(1.1);
}

/* Frontend group */
.tech-group:nth-child(1) .tech-icon-wrapper:nth-child(1) {
  animation-name: orbit1;
}
.tech-group:nth-child(1) .tech-icon-wrapper:nth-child(2) {
  animation-name: orbit2;
}
.tech-group:nth-child(1) .tech-icon-wrapper:nth-child(3) {
  animation-name: orbit3;
}

/* Backend group */
.tech-group:nth-child(2) .tech-icon-wrapper:nth-child(1) {
  animation-name: orbit4;
}
.tech-group:nth-child(2) .tech-icon-wrapper:nth-child(2) {
  animation-name: orbit5;
}
.tech-group:nth-child(2) .tech-icon-wrapper:nth-child(3) {
  animation-name: orbit6;
}

/* DevOps group */
.tech-group:nth-child(3) .tech-icon-wrapper:nth-child(1) {
  animation-name: orbit7;
}
.tech-group:nth-child(3) .tech-icon-wrapper:nth-child(2) {
  animation-name: orbit8;
}
.tech-group:nth-child(3) .tech-icon-wrapper:nth-child(3) {
  animation-name: orbit9;
}

/* Orbital animations - each with unique paths */
@keyframes orbit1 {
  0% { left: 20%; top: 0; }
  25% { left: 30%; top: 20px; }
  50% { left: 40%; top: 0; }
  75% { left: 50%; top: -20px; }
  100% { left: 60%; top: 0; }
}

@keyframes orbit2 {
  0% { left: 45%; top: 10px; }
  25% { left: 55%; top: -10px; }
  50% { left: 65%; top: 10px; }
  75% { left: 55%; top: 30px; }
  100% { left: 45%; top: 10px; }
}

@keyframes orbit3 {
  0% { left: 70%; top: 0; }
  25% { left: 60%; top: 15px; }
  50% { left: 50%; top: 0; }
  75% { left: 40%; top: -15px; }
  100% { left: 30%; top: 0; }
}

@keyframes orbit4 {
  0% { left: 25%; top: 10px; }
  25% { left: 35%; top: -10px; }
  50% { left: 45%; top: 10px; }
  75% { left: 35%; top: 30px; }
  100% { left: 25%; top: 10px; }
}

@keyframes orbit5 {
  0% { left: 50%; top: -10px; }
  25% { left: 60%; top: 10px; }
  50% { left: 70%; top: -10px; }
  75% { left: 60%; top: -30px; }
  100% { left: 50%; top: -10px; }
}

@keyframes orbit6 {
  0% { left: 75%; top: 5px; }
  25% { left: 65%; top: 25px; }
  50% { left: 55%; top: 5px; }
  75% { left: 65%; top: -15px; }
  100% { left: 75%; top: 5px; }
}

@keyframes orbit7 {
  0% { left: 30%; top: -5px; }
  25% { left: 40%; top: 15px; }
  50% { left: 50%; top: -5px; }
  75% { left: 40%; top: -25px; }
  100% { left: 30%; top: -5px; }
}

@keyframes orbit8 {
  0% { left: 55%; top: 0; }
  25% { left: 65%; top: -20px; }
  50% { left: 75%; top: 0; }
  75% { left: 65%; top: 20px; }
  100% { left: 55%; top: 0; }
}

@keyframes orbit9 {
  0% { left: 70%; top: -10px; }
  25% { left: 60%; top: 10px; }
  50% { left: 50%; top: -10px; }
  75% { left: 40%; top: -30px; }
  100% { left: 30%; top: -10px; }
}

/* Add responsive adjustments */
@media (max-width: 768px) {
  .tech-galaxy {
    gap: 1.5rem;
  }
  
  .tech-group {
    height: 80px;
  }
  
  .tech-icons-container {
    height: 60px;
  }
  
  .tech-icon-wrapper {
    width: 40px;
    height: 40px;
  }
}
